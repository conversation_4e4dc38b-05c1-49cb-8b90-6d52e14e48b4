---
title: Anthropic
sidebarTitle: Anthropic
---

This documentation describes the integration of MindsDB with [Anthropic](https://www.anthropic.com/), an AI research company.
The integration allows for the deployment of Anthropic models within MindsDB, providing the models with access to data from various data sources.

## Prerequisites

Before proceeding, ensure the following prerequisites are met:

1. Install MindsDB locally via [Docker](https://docs.mindsdb.com/setup/self-hosted/docker) or [Docker Desktop](https://docs.mindsdb.com/setup/self-hosted/docker-desktop).
2. To use Anthropic within MindsDB, install the required dependencies following [this instruction](https://docs.mindsdb.com/setup/self-hosted/docker#install-dependencies).
3. Obtain the Anthropic API key required to deploy and use Anthropic models within MindsDB. Follow the [instructions for obtaining the API key](https://docs.anthropic.com/claude/docs/getting-access-to-claude).

## Setup

Create an AI engine from the [Anthropic handler](https://github.com/mindsdb/mindsdb/tree/main/mindsdb/integrations/handlers/anthropic_handler).

```sql
CREATE ML_ENGINE anthropic_engine
FROM anthropic
USING
    anthropic_api_key = 'your-anthropic-api-key';
```

Create a model using `anthropic_engine` as an engine.

```sql
CREATE MODEL anthropic_model
PREDICT target_column
USING
      engine = 'anthropic_engine',  -- engine name as created via CREATE ML_ENGINE
      column = 'column_name',       -- column that stores input/question to the model
      max_tokens = <integer>,       -- max number of tokens to be generated by the model (default is 100)
      model = 'model_name';         -- choose one of 'claude-instant-1.2', 'claude-2.1', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229' (default is 'claude-2.1')
```

<Info>

The integrations between Anthropic and MindsDB was implemented using [Anthropic Python SDK](https://github.com/anthropics/anthropic-sdk-python).
</Info>

## Usage

The following usage examples utilize `anthropic_engine` to create a model with the `CREATE MODEL` statement.

Create and deploy the Anthropic model within MindsDB to ask any question.

```sql
CREATE MODEL anthropic_model
PREDICT answer
USING
    column = 'question',
    engine = 'anthropic_engine',
    max_tokens = 300,
    model = 'claude-2.1'; -- choose one of 'claude-instant-1.2', 'claude-2.1', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229'
```

Where:

| Name              | Description                                                               |
|-------------------|---------------------------------------------------------------------------|
| `column`          | It defines the prompt to the model.                                       |
| `engine`          | It defines the Anthropic engine.                                          |
| `max_tokens`      | It defines the maximum number of tokens to generate before stopping.      |
| `model`           | It defines model that will complete your prompt.                          |

<Info>

**Default Model**

When you create an Anthropic model in MindsDB, it uses the `claude-2.1` model by default. But you can use other available models by passing the model name to the `model` parameter in the `USING` clause of the `CREATE MODEL` statement.
</Info>

<Info>

**Default Max Tokens**

When you create an Anthropic model in MindsDB, it uses 100 tokens as the maximum by default. But you can adjust this value by passing it to the `max_tokens` parameter in the `USING` clause of the `CREATE MODEL` statement.
</Info>

Query the model to get predictions.

```sql
SELECT question, answer
FROM anthropic_model
WHERE question = 'Where is Stockholm located?';
```

Here is the output:

```sql
+-----------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------+
| question                    | answer                                                                                                                                             |
+-----------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------+
| Where is Stockholm located? |  Stockholm is the capital and largest city of Sweden. It is located on Sweden's south-central east coast, where Lake Mälaren meets the Baltic Sea. |
+-----------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------+
```

<Tip>

**Next Steps**

Go to the [Use Cases](https://docs.mindsdb.com/use-cases/overview) section to see more examples.
</Tip>
