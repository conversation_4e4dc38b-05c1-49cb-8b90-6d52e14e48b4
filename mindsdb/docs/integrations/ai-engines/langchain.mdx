---
title: <PERSON><PERSON><PERSON><PERSON>
sidebarTitle: <PERSON><PERSON><PERSON><PERSON>
---

This documentation describes the integration of MindsDB with [<PERSON><PERSON>hai<PERSON>](https://www.langchain.com/), a framework for developing applications powered by language models.
The integration allows for the deployment of LangChain models within MindsDB, providing the models with access to data from various data sources.

## Prerequisites

Before proceeding, ensure the following prerequisites are met:

1. Install MindsDB locally via [Docker](https://docs.mindsdb.com/setup/self-hosted/docker) or [Docker Desktop](https://docs.mindsdb.com/setup/self-hosted/docker-desktop).
2. To use LangChain within MindsDB, install the required dependencies following [this instruction](https://docs.mindsdb.com/setup/self-hosted/docker#install-dependencies).
3. Obtain the API key for a selected model provider that you want to use through LangChain.

<Info>
Available models include the following:

- OpenAI ([how to get the API key](https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key))
- Anthropic ([how to get the API key](https://docs.anthropic.com/claude/docs/getting-access-to-claude))
- Anyscale ([how to get the API key](https://docs.endpoints.anyscale.com/guides/authenticate/))
- Google ([how to get the API key](http://aistudio.google.com/))
- Ollama ([how to download Ollama](https://ollama.com/download))
- LiteLLM (use the API key of the model used via LiteLLM)
- MindsDB (use any model created within MindsDB)
</Info>

## Setup

Create an AI engine from the [LangChain handler](https://github.com/mindsdb/mindsdb/tree/main/mindsdb/integrations/handlers/langchain_handler).

```sql
CREATE ML_ENGINE langchain_engine
FROM langchain
USING
      serper_api_key = 'your-serper-api-key'; -- it is an optional parameter (if provided, the model will use serper.dev search to enhance the output)
```

Create a model using `langchain_engine` as an engine and a selected model provider.

```sql
CREATE MODEL langchain_model
PREDICT target_column
USING
      engine = 'langchain_engine',           -- engine name as created via CREATE ML_ENGINE
      <provider>_api_key = 'api-key-value',  -- replace <provider> with one of the available values (openai, anthropic, anyscale, google, litellm)
      model_name = 'model-name',             -- optional, model to be used (for example, 'gpt-4' if 'openai_api_key' provided)
      prompt_template = 'message to the model that may include some {{input}} columns as variables',
      max_tokens = 4096; -- defines the maximum number of tokens
```

<Info>
This handler supports [tracing features for LangChain via LangFuse](https://langfuse.com/docs/integrations/langchain/tracing). To use it, provide the following parameters in the `USING` clause:

- `langfuse_host`,
- `langfuse_public_key`,
- `langfuse_secret_key`.
</Info>

<Info>
There are three different tools utilized by this agent:

* **MindsDB** is the internal MindsDB executor.
* **Metadata** fetches the metadata information for the available tables.
* **Write** is able to write agent responses into a MindsDB data source.

Each tool exposes the internal MindsDB executor in a different way to perform its tasks, effectively enabling the agent model to read from (and potentially write to) data sources or models available in the active MindsDB project.
</Info>

Create a conversational model using `langchain_engine` as an engine and a selected model provider.

<AccordionGroup>

<Accordion title="OpenAI">
```sql
CREATE MODEL langchain_openai_model
PREDICT answer
USING
     engine = 'langchain_engine',       -- engine name as created via CREATE ML_ENGINE
     provider = 'openai',               -- one of the available providers
     openai_api_key = 'api-key-value',
     model_name = 'gpt-3.5-turbo',      -- choose one of the available OpenAI models
     mode = 'conversational',           -- conversational mode
     user_column = 'question',          -- column name that stores input from the user
     assistant_column = 'answer',       -- column name that stores output of the model (see PREDICT column)
     base_url = 'base-url-value',       -- optional, default is https://api.openai.com/v1/
     verbose = True,
     prompt_template = 'Answer the users input in a helpful way: {{question}}',
     max_tokens = 4096;
```
</Accordion>

<Accordion title="Anthropic">
```sql
CREATE MODEL langchain_anthropic_model
PREDICT answer
USING
     engine = 'langchain_engine',          -- engine name as created via CREATE ML_ENGINE
     provider = 'anthropic',               -- one of the available providers
     anthropic_api_key = 'api-key-value',
     model_name = 'claude-2.1',            -- choose one of the available OpenAI models
     mode = 'conversational',              -- conversational mode
     user_column = 'question',             -- column name that stores input from the user
     assistant_column = 'answer',          -- column name that stores output of the model (see PREDICT column)
     verbose = True,
     prompt_template = 'Answer the users input in a helpful way: {{question}}',
     max_tokens = 4096;
```
</Accordion>

<Accordion title="Anyscale">
```sql
CREATE MODEL langchain_anyscale_model
PREDICT answer 
USING
     engine = 'langchain_engine',                         -- engine name as created via CREATE ML_ENGINE
     provider = 'anyscale',                               -- one of the available providers
     anyscale_api_key = 'api-key-value',
     model_name = 'mistralai/Mistral-7B-Instruct-v0.1',   -- choose one of the models available from Anyscale
     mode = 'conversational',                             -- conversational mode
     user_column = 'question',                            -- column name that stores input from the user
     assistant_column = 'answer',                         -- column name that stores output of the model (see PREDICT column) 
     base_url = 'https://api.endpoints.anyscale.com/v1',
     verbose = True,
     prompt_template = 'Answer the users input in a helpful way: {{question}}',
     max_tokens = 4096;
```
</Accordion>

<Accordion title="Ollama">
```sql
CREATE MODEL langchain_ollama_model
PREDICT answer 
USING
     engine = 'langchain_engine',       -- engine name as created via CREATE ML_ENGINE
     provider = 'ollama',               -- one of the available providers
     model_name = 'llama2',             -- choose one of the models available from Ollama
     mode = 'conversational',           -- conversational mode
     user_column = 'question',          -- column name that stores input from the user
     assistant_column = 'answer',       -- column name that stores output of the model (see PREDICT column) 
     verbose = True,
     prompt_template = 'Answer the users input in a helpful way: {{question}}',
     max_tokens = 4096;
```
Ensure to have Ollama set up locally by following this guide on [how to download Ollama](https://ollama.com/download).
</Accordion>

<Accordion title="LiteLLM">
```sql
CREATE MODEL langchain_litellm_model
PREDICT answer 
USING
     engine = 'langchain_engine',            -- engine name as created via CREATE ML_ENGINE
     provider = 'litellm',                   -- one of the available providers
     litellm_api_key = 'api-key-value',
     model_name = 'gpt-4',                   -- choose one of the models available from LiteLLM
     mode = 'conversational',                -- conversational mode
     user_column = 'question',               -- column name that stores input from the user
     assistant_column = 'answer',            -- column name that stores output of the model (see PREDICT column)
     base_url = 'https://ai.dev.mindsdb.com',
     verbose = True,
     prompt_template = 'Answer the users input in a helpful way: {{question}}',
     max_tokens = 4096;
```
</Accordion>

<Accordion title="Google">
```sql
CREATE MODEL langchain_google_model
PREDICT answer 
USING
     engine = 'langchain_engine',            -- engine name as created via CREATE ML_ENGINE
     provider = 'google',                    -- one of the available providers
     google_api_key = 'api-key-value',
     model_name = 'gemini-1.5-flash',        -- choose one of the models available from Google
     mode = 'conversational',                -- conversational mode
     user_column = 'question',               -- column name that stores input from the user
     assistant_column = 'answer',            -- column name that stores output of the model (see PREDICT column)
     verbose = True,
     prompt_template = 'Answer the users input in a helpful way: {{question}}',
     max_tokens = 4096;
```
</Accordion>

<Accordion title="MindsDB">
```sql
CREATE MODEL langchain_mindsdb_model
PREDICT answer
USING
     engine = 'langchain_engine',       -- engine name as created via CREATE ML_ENGINE
     provider = 'mindsdb',              -- one of the available providers
     model_name = 'mindsdb_model',      -- any model created within MindsDB
     mode = 'conversational',           -- conversational mode
     user_column = 'question',          -- column name that stores input from the user
     assistant_column = 'answer',       -- column name that stores output of the model (see PREDICT column)
     verbose = True,
     prompt_template = 'Answer the users input in a helpful way: {{question}}',
     max_tokens = 4096;
```
</Accordion>

</AccordionGroup>

## Usage

The following usage examples utilize `langchain_engine` to create a model with the `CREATE MODEL` statement.

Create a model that will be used to ask questions.

```sql
CREATE ML_ENGINE langchain_engine_google
FROM langchain;

CREATE MODEL langchain_google_model
PREDICT answer 
USING
     engine = 'langchain_engine_google',
     provider = 'google',
     google_api_key = 'api-key-value',
     model_name = 'gemini-1.5-flash',
     mode = 'conversational',
     user_column = 'question',
     assistant_column = 'answer',
     verbose = True,
     prompt_template = 'Answer the users input in a helpful way: {{question}}',
     max_tokens = 4096;
```

Ask questions.

```sql
SELECT question, answer
FROM langchain_google_model
WHERE question = 'How many planets are in the solar system?';
```

<Tip>
**Next Steps**

Go to the [Use Cases](https://docs.mindsdb.com/use-cases/overview) section to see more examples.
</Tip>
