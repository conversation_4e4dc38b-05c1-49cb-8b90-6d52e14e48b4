---
title: Replicate (Text2Img)
sidebarTitle: Replicate (Text2Img)
---

This handler was implemented using the `replicate` library that is provided by <PERSON>licate.

The required arguments to establish a connection are,

* model_name: Model name which you want to access in MindsDB. e.g 'air-forever/kandinsky-2'
* version: version hash/id which you want to use in MindsDB.
* api_key: API key from Replicate Platform you can found [here](https://replicate.com/account/api-tokens).

<Note> 

Before you can use Replicate, it's essential to authenticate by setting your API token in an environment variable named REPLICATE_API_TOKEN.
 This token acts as a key to enable access to Replicate's features.

1. Using pip:

If you're working in a standard Python environment (using pip for package management), set your token as an environment variable by running the following command 
in your terminal:

On Linux, Mac:
```
export REPLICATE_API_TOKEN='YOUR_TOKEN'
```
On Windows:

```
set REPLICATE_API_TOKEN=YOUR_TOKEN
```


2. Using Docker:

For Docker users, the process slightly differs. You need to pass the environment variable directly to the Docker container when running it. Use this command:

```
docker run -e REPLICATE_API_TOKEN='YOUR_TOKEN' -p 47334:47334 -p 47335:47335 mindsdb/mindsdb
```

Again, replace 'YOUR_TOKEN' with your actual Replicate API token.
</Note>

## Usage
To use this handler and connect to a Replicate cluster in MindsDB, you need an account on Replicate. Make sure to create an account by following this [link](https://replicate.com/signin?next=/account/api-tokens).


To establish the connection and create a model in MindsDB, use the following syntax:
```sql

CREATE MODEL aiforever
PREDICT url
USING
    engine = 'replicate',
    model_name= 'ai-forever/kandinsky-2',
    version ='2af375da21c5b824a84e1c459f45b69a117ec8649c2aa974112d7cf1840fc0ce',
    api_key = 'r8_BpO.........................';
```

You can use the DESCRIBE PREDICTOR query to see the available parameters that you can specify to customize your predictions:
```sql
DESCRIBE PREDICTOR mindsdb.aiforever.features;
```

### Output
```sql
+---------------------+-------------------+--------------------------------------------------------+---------+
| inputs              | default           | description                                            | type    |
+---------------------+-------------------+--------------------------------------------------------+---------+
| width               | 512               | Choose width. Lower the setting if out of memory.      | -       |
| height              | 512               | Choose height. Lower the setting if out of memory.     | -       |
| prompt              | red cat, 4k photo | Input Prompt                                           | string  |
| scheduler           | p_sampler         | Choose a scheduler                                     | -       |
| batch_size          | 1                 | Choose batch size. Lower the setting if out of memory. | -       |
| prior_steps         | 5                 | -                                                      | string  |
| guidance_scale      | 4                 | Scale for classifier-free guidance                     | number  |
| prior_cf_scale      | 4                 | -                                                      | integer |
| num_inference_steps | 50                | Number of denoising steps                              | integer |
+---------------------+-------------------+--------------------------------------------------------+---------+
```

Now, you can use the established connection to query your ML Model as follows:
```sql
SELECT *
FROM aiforever
WHERE prompt='Great warrior Arjun from Mahabharata, looking at camera,cinematic lighting, 4k quality';
```

### Output

![GENERATE_IMAGE](/assets/integrations/Arjuna.png)

- IMPORTANT NOTE: PREDICTED **URL** will only work for **24 hours** after prediction.

> Note: Replicate provides only a few free predictions, so choose your predictions wisely. Don't let the machines have all the fun, save some for yourself! 😉
