---
title: Vertex AI
sidebarTitle: Vertex AI
---

This documentation describes the integration of MindsDB with [Vertex AI](https://cloud.google.com/vertex-ai), a machine learning platform that lets you train and deploy ML models and AI applications, and customize large language models (LLMs) for use in AI-powered applications.
The integration allows for the deployment of Vertex AI models within MindsDB, providing the models with access to data from various data sources.

## Prerequisites

Before proceeding, ensure the following prerequisites are met:

1. Install MindsDB locally via [Docker](/setup/self-hosted/docker) or [Docker Desktop](/setup/self-hosted/docker-desktop).
2. To use Vertex AI within MindsDB, install the required dependencies following [this instruction](/setup/self-hosted/docker#install-dependencies).

## Setup

Create an AI engine from the [Vertex AI handler](https://github.com/mindsdb/mindsdb/tree/main/mindsdb/integrations/handlers/vertex_handler).

This command creates a config object that can be used in client creation step.

```sql
CREATE ML_ENGINE vertex_engine
FROM vertex
USING
      project_id = "mindsdb-401709",
      location = "us-central1",
      staging_bucket = "gs://my_staging_bucket",
      experiment = "my-experiment",
      experiment_description = "my experiment description",
      service_account = {
        <paste service account keys here>
      };
```

Create a model using `vertex_engine` as an engine.

This command authenticates client to a Vertex account using config from previous step. If the endpoint for the model already exists, we create this model in MindsDB. Otherwise, we create and deploy the model to the endpoint before creating this model in MindsDB.

```sql
CREATE MODEL vertex_model
PREDICT target_column
USING
      engine = 'vertex_engine',  -- engine name as created via CREATE ML_ENGINE
      model_name = 'model_name', -- choose one of models from your project
      custom_model = value;      -- indicate whether it is a custom model (True) or not (False)
;
```

## Usage

The following usage examples utilize `vertex_engine` to create a model with the `CREATE MODEL` statement.

Detect anomaly using a custom model stored in Vertex AI.

```sql
CREATE MODEL vertex_model
PREDICT cut
USING 
    engine = 'vertex',
    model_name = 'diamonds_anomaly_detection',
    custom_model = True;
```

Query the model to get predictions by joining it with the data table.

```sql
SELECT d.cut, m.cut AS anomaly
FROM data_table as d
JOIN vertex_model as m;
```

<Tip>
**Next Steps**

Go to the [Use Cases](/use-cases/overview) section to see more examples.>
</Tip>
