---
title: OrioleDB
sidebarTitle: OrioleDB
---

This is the implementation of the OrioleDB data handler for MindsDB.

[OrioleDB](https://www.orioledata.com/) is a new storage engine for PostgreSQL, bringing a modern approach to database capacity, capabilities, and performance to the world's most-loved database platform. It consists of an extension, building on the innovative table access method framework and other standard Postgres extension interfaces. By extending and enhancing the current table access methods, OrioleDB opens the door to a future of more powerful storage models that are optimized for cloud and modern hardware architectures.

## Prerequisites

Before proceeding, ensure the following prerequisites are met:

1. Install MindsDB locally via [Docker](/setup/self-hosted/docker) or [Docker Desktop](/setup/self-hosted/docker-desktop).
2. To connect OrioleDB to MindsDB, install the required dependencies following [this instruction](/setup/self-hosted/docker#install-dependencies).
3. Install or ensure access to OrioleDB.

## Implementation

This handler is implemented by extending the PostgreSQL data handler.

The required arguments to establish a connection are as follows:

* `user` is the database user.
* `password` is the database password.
* `host` is the host name, IP address, or URL.
* `port` is the port used to make TCP/IP connection.
* `server` is the OrioleDB server.
* `database` is the database name.

## Usage

In order to make use of this handler and connect to the OrioleDB server in MindsDB, the following syntax can be used:

```sql
CREATE DATABASE orioledb_datasource
WITH ENGINE = 'orioledb',
PARAMETERS = {
  "user": "orioledb_user",
  "password": "password",
  "host": "127.0.0.1",
  "port": 55505,
  "server": "server_name",
  "database": "oriole_db"
};
```

You can use this established connection to query your table as follows:

```sql
SELECT *
FROM orioledb_data.demo_table
LIMIT 10;
```
