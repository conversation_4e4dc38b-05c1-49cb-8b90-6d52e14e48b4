---
title: OceanBase
sidebarTitle: OceanBase
---

This is the implementation of the OceanBase data handler for MindsDB.

OceanBase is a distributed relational database. It is the only distributed database in the world that has broken both TPC-C and TPC-H records. OceanBase adopts an independently developed integrated architecture, which encompasses both the scalability of a distributed architecture and the performance advantage of a centralized architecture. It supports hybrid transaction/analytical processing (HTAP) with one engine. Its features include strong data consistency, high availability, high performance, online scalability, high compatibility with SQL and mainstream relational databases, transparency to applications, and a high cost/performance ratio.

## Prerequisites

Before proceeding, ensure the following prerequisites are met:

1. Install MindsDB locally via [Docker](/setup/self-hosted/docker) or [Docker Desktop](/setup/self-hosted/docker-desktop).
2. To connect OceanBase to MindsDB, install the required dependencies following [this instruction](/setup/self-hosted/docker#install-dependencies).
3. Install or ensure access to OceanBase.

## Implementation

This handler is implemented by extending the MySQL data handler.

The required arguments to establish a connection are as follows:

* `user` is the database user.
* `password` is the database password.
* `host` is the host name, IP address, or URL.
* `port` is the port used to make TCP/IP connection.
* `database` is the database name.

## Usage

In order to make use of this handler and connect to the OceanBase server in MindsDB, the following syntax can be used:

```sql
CREATE DATABASE oceanbase_datasource
WITH
  ENGINE = 'oceanbase',
  PARAMETERS = {
    "host": "127.0.0.1",
    "user": "oceanbase_user",
    "password": "password",
    "port": 2881,
    "database": "oceanbase_db"
  };
```

Now, you can use this established connection to query your database as follows:

```sql
SELECT *
FROM oceanbase_datasource.demo_table
LIMIT 10;
```
