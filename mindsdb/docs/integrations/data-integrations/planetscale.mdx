---
title: PlanetScale
sidebarTitle: PlanetScale
---

This is the implementation of the PlanetScale data handler for MindsDB.

[PlanetScale](https://planetscale.com/) is a MySQL-compatible, serverless database platform.

## Prerequisites

Before proceeding, ensure the following prerequisites are met:

1. Install MindsDB locally via [Docker](/setup/self-hosted/docker) or [Docker Desktop](/setup/self-hosted/docker-desktop).
2. To connect PlanetScale to MindsDB, install the required dependencies following [this instruction](/setup/self-hosted/docker#install-dependencies).
3. Install or ensure access to PlanetScale.

## Implementation

This handler is implemented by extending the MySQL data handler.

The required arguments to establish a connection are as follows:

* `user` is the database user.
* `password` is the database password.
* `host` is the host name, IP address, or URL.
* `port` is the port used to make TCP/IP connection.
* `database` is the database name.

## Usage

In order to make use of this handler and connect to the PlanetScale database in MindsDB, the following syntax can be used:

```sql
CREATE DATABASE planetscale_datasource
WITH
  ENGINE = 'planet_scale',
  PARAMETERS = {
    "host": "127.0.0.1",
    "port": 3306,
    "user": "planetscale_user",
    "password": "password",
    "database": "planetscale_db"
  };
```

You can use this established connection to query your table as follows:

```sql
SELECT *
FROM planetscale_datasource.my_table;
```
