---
title: Model Management
sidebarTitle: Model Management
icon: "bars-progress"
---

MindsDB abstracts AI models, making them accessible from enterprise data environments.

<p align="center">
  <img src="/assets/model-management.png" />
</p>

MindsDB enables you to manage every aspect of AI models. With MindsDB, you can [CREATE MODEL](/mindsdb_sql/sql/create/model), [FINETUNE](/mindsdb_sql/sql/api/finetune), [RETRAIN](/mindsdb_sql/sql/api/retrain), and more.

* [Deploy](/mindsdb_sql/sql/create/model)
<br></br>You can [create, train, and deploy AI models](/mindsdb_sql/sql/create/model) based on popular [AI/ML frameworks](/integrations/ai-overview) within MindsDB.

* [Fine-tune](/mindsdb_sql/sql/api/finetune)
<br></br>You can [fine-tune models](/mindsdb_sql/sql/api/finetune) with data from various [data sources](/integrations/data-overview) connected to MindsDB. Check out [examples here](/use-cases/automated_finetuning/overview).

* [Automate](/mindsdb_sql/sql/create/jobs)
<br></br>You can automate tasks, including retraining or fine-tuning of AI models, to keep your AI system up-to-date. See [examples here](/use-cases/ai_workflow_automation/overview).

<Tip>
Go ahead and create an AI model!

Use [SQL API](/mindsdb_sql/overview), [REST API](/rest/overview), or one of the [SDKs](/sdks/overview) to create and deploy AI models within MindsDB.
</Tip>
