name: Test on Push

permissions:
    contents: read

on:
  pull_request:
    branches: [main]
  workflow_dispatch:

defaults:
  run:
    shell: bash

# Cancel any existing runs of this workflow on the same branch/pr
# We always want to build/deploy/test a new commit over an older one
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.ref }}
  cancel-in-progress: true

jobs:
  # Sets an output var to indicate if all changes are in docs files
  # This output is used to skip running code checks on docs changes
  changes:
    name: Filter changed files
    runs-on: mdb-dev
    outputs:
      not-docs: ${{ steps.filter.outputs.not-docs }}
    steps:
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          predicate-quantifier: "every"
          filters: |
            not-docs:
              - '!docs/**'  
              - '!**/*.md'

  # Run all of our static code checks here
  code_checking:
    name: Run static code checks
    runs-on: mdb-dev
    needs: changes
    steps:
      - name: Checkout
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        uses: actions/checkout@v4
        with:
          # required to grab the history of the PR for pre-commit to work out what's changed
          fetch-depth: 0 
      # Install python and setup UV
      - name: Setup uv
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        uses: astral-sh/setup-uv@v5
        with:
          # Place cache in the tool dir because we mount this in our runnners
          cache-local-path: "/home/<USER>/_work/_tool/uv-local-cache"
          prune-cache: false
          # I can't work out how to use vars.CI_PYTHON_VERSION for forks here in a nice way, so we have to hardcode it for forks
          python-version: ${{ vars.CI_PYTHON_VERSION || '3.10' }}

      # Checks the codebase for print() statements and fails if any are found
      # We should be using loggers instead
      - name: Check for print statements
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        run: |
          # The pyproject file confuses uv: https://github.com/astral-sh/uv/issues/6838
          rm pyproject.toml
          uv run tests/scripts/check_print_statements.py
      
      - name: Install MDB dev requirements
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        run: |
          uv pip install -r requirements/requirements-dev.txt

      # Run pre-commit on all changed files
      # See .pre-commit-config.yaml for the list of checks
      - name: Run pre-commit
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        run: |
          pre-commit run --show-diff-on-failure --color=always --from-ref ${{ github.event.pull_request.base.sha }} --to-ref ${{ github.event.pull_request.head.sha }}

      # Runs a few different checks against our many requirements files
      # to make sure they're in order
      - name: Check requirements files
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        run: |
          uv run tests/scripts/check_requirements.py

  # Creates a matrix of environments to test against using matrix_includes.json
  # Used for installation checks below
  matrix_prep:
    name: Prepare matrix
    runs-on: mdb-dev
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - uses: actions/checkout@v4
      - id: set-matrix
        uses: JoshuaTheMiller/conditional-build-matrix@v2.0.1
        with:
          filter: "[?runOnBranch==`${{ github.ref }}` || runOnBranch==`always`]"

  # Check that our pip package is able to be installed in all of our supported environments
  check_install:
    name: Check pip installation
    needs: [changes, matrix_prep, code_checking]
    strategy:
      matrix: ${{fromJson(needs.matrix_prep.outputs.matrix)}}
    runs-on: ${{ matrix.runs_on }}
    steps:
      - name: Checkout
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        uses: actions/checkout@v4
      # Install python and setup UV
      - name: Setup uv
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          python-version: ${{ matrix.python-version }}
      - name: Check pip package builds and installs
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        run: |
          # Install dev requirements and build our pip package
          uv pip install -r requirements/requirements-dev.txt
          python setup.py sdist

          # Install from the pip package
          # If we install from source, we don't know if the pip package is installable.
          cd dist
          uv pip install *.tar.gz --prerelease=allow

  unit_tests:
    name: Run Unit Tests
    needs: [changes, matrix_prep, code_checking]
    strategy:
      matrix: ${{fromJson(needs.matrix_prep.outputs.matrix)}}
    runs-on: ${{ matrix.runs_on }}
    if: github.ref_type == 'branch'
    steps:
      - name: Checkout
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        uses: actions/checkout@v4
      # Install python and setup UV
      - name: Setup uv
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        run: |
          uv pip install -r requirements/requirements-test.txt
          # TODO: for now some tests rely on lightwood
          uv pip install . --prerelease=allow \
            .[lightwood] \
            .[clickhouse] \
            .[snowflake] \
            .[web] \
            .[redshift] \
            .[bigquery] \
            .[databricks] \
            .[oracle] \
            .[slack] \
            .[github] \
            .[ms_teams] \
            .[salesforce] \
            .[mysql] \
            .[chromadb]
          uv pip freeze
          git clone https://github.com/mindsdb/mindsdb_sql_parser.git parser_tests
      - name: Install mssql dependencies
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        run: |
          # Set FreeTDS build flags only on macOS
          # Currently broken due to new cython version, see https://github.com/pymssql/pymssql/issues/937
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            echo "Skipping pymssql build on macOS temporarily"
            # brew install freetds
            # echo "Setting FreeTDS flags for macOS pymssql build"
            # export LDFLAGS="-L$(brew --prefix freetds)/lib"
            # export CFLAGS="-I$(brew --prefix freetds)/include"
            # uv pip install pymssql==2.3.1 --no-binary :all:
          else
            uv pip install .[mssql]
          fi
          
      - name: Run unit tests
        if: ${{ needs.changes.outputs.not-docs == 'true' }}
        run: |
          make unit_tests_slow
