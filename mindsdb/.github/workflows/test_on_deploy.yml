name: Test on Deploy

permissions:
    contents: read

on:
  workflow_call:
    inputs:
      git-sha:
        required: false
        type: string
        default: ""
      deploy-env:
        required: true
        type: string
    secrets:
      OPENAI_API_KEY:
        required: true
  workflow_dispatch:
    inputs:
      git-sha:
        required: false
        type: string
        default: ""
      deploy-env:
        required: true
        type: string
    secrets:
      OPENAI_API_KEY:
        required: true

defaults:
  run:
    shell: bash

env:
  UV_LINK_MODE: "symlink"

jobs:
  changes:
    name: Filter changed files
    runs-on: mdb-dev
    outputs:
      not-docs: ${{ steps.filter.outputs.not-docs }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          predicate-quantifier: "every"
          filters: |
            not-docs:
              - '!docs/**'  
              - '!**/*.md'
              - '!.github/workflows/build_deploy_dev.yml'
              - '!.github/workflows/test_on_deploy.yml'
  # Run our integration tests
  test:
    needs: changes
    environment:
      name: ${{ inputs.deploy-env }}
      url: ${{ vars.ENV_URL }}
    name: Run integration tests on deploy
    runs-on: mdb-dev
    if: ${{ github.ref_type == 'branch' && needs.changes.outputs.not-docs == 'true' }}
    steps:
    - uses: actions/checkout@v4
      with:
        ref: ${{ inputs.git-sha }}
    - name: Setup uv
      uses: astral-sh/setup-uv@v5
      with:
        # Place cache in the tool dir because we mount this in our runnners
        cache-local-path: "/home/<USER>/_work/_tool/uv-local-cache"
        prune-cache: false
        python-version: ${{ vars.CI_PYTHON_VERSION || '3.10' }}
    - name: Install dependencies
      run: |
        uv pip install -r requirements/requirements-test.txt

    - name: Run Integration Tests on Deploy
      run: |
          make integration_tests_slow
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        INTERNAL_URL: ${{ vars.INTERNAL_URL }}
    